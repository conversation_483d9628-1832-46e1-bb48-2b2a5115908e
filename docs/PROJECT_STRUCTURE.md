# ChatSharePanel 项目结构

## 文件组织

```
chatsharepanel/
├── src/                      # 源代码目录
│   ├── main.go              # 主程序入口，路由配置
│   ├── Dockerfile           # Docker镜像构建配置
│   ├── models/              # 数据模型
│   │   └── models.go        # 数据结构定义
│   ├── handlers/            # API处理器
│   │   ├── user.go          # 用户相关API处理
│   │   └── cdk.go           # 兑换码相关API处理
│   ├── services/            # 业务服务
│   │   └── logs.go          # 日志记录服务
│   ├── database/            # 数据库层
│   │   └── database.go      # 数据库连接配置
│   └── utils/               # 工具函数
│       └── utils.go         # 通用工具函数
├── sql/                     # 数据库脚本
│   ├── init.sql             # 兑换码表初始化脚本
│   └── log_table.sql        # 日志表初始化脚本
├── docs/                    # 项目文档
│   └── PROJECT_STRUCTURE.md # 项目结构说明
├── docker-compose.yml       # 开发环境Docker编排
├── production-compose.yml   # 生产环境Docker编排
├── config.env               # 环境变量配置
├── README.md                # 项目说明
├── .gitignore              # Git忽略文件
├── go.mod                  # Go模块依赖
└── go.sum                  # Go模块校验和
```

## 模块功能

### 1. src/main.go - 应用入口
- 应用程序主入口
- HTTP路由配置
- 中间件设置
- 服务器启动

### 2. src/models/ - 数据模型层
- `User` - 用户数据结构
- `CDKCode` - 兑换码数据结构
- `CDKUseRequest` - 兑换码使用请求
- `CDKUseResponse` - 兑换码使用响应
- `APIResponse` - 统一API响应格式

### 3. src/handlers/ - API处理层
#### user.go - 用户管理
- 用户查询 (`getUserByToken`)
- 用户创建 (`createUser`)
- 用户信息更新 (`updateUserTime`)
- 自定义token验证 (`validateCustomToken`)
- 用户信息查询API (`getUserInfo`)

#### cdk.go - 兑换码管理
- 兑换码查询 (`getCDKByCode`)
- 兑换码使用状态更新 (`updateCDKUsage`)
- 多用户使用检查 (`hasUserUsedCDK`)
- 兑换码使用API (`useCDK`)

### 4. src/services/ - 业务服务层
#### logs.go - 日志记录服务
- 操作日志记录 (`LogOperation`)
- 兑换码用户关系记录 (`LogCDKUserRelation`)
- 成功操作日志 (`LogCDKUseSuccess`, `LogUserCreateSuccess`, `LogUserExtendSuccess`)
- 失败操作日志 (`LogCDKUseFailure`, `LogValidationError`, `LogSystemError`)

### 5. src/database/ - 数据库层
- 数据库连接初始化
- 环境变量加载
- 配置管理

### 6. src/utils/ - 工具层
- 随机token生成 (`generateToken`)
- 字符串处理工具

### 7. src/Dockerfile - 容器化配置
- 多阶段构建配置
- Go应用编译和打包
- 轻量级运行时镜像

## 数据库表结构

### 核心表
- `chatgpt_user` - 用户表
- `cdk_code` - 兑换码表

### 日志表
- `operation_logs` - 操作日志表
- `cdk_user_relations` - 兑换码用户关系表

## API 接口

### 1. 用户信息查询
- **URL**: `GET /api/user/info`
- **参数**: `token` (query参数)
- **功能**: 查询用户信息

### 2. 兑换码使用
- **URL**: `POST /api/cdk/use`
- **参数**: JSON body
  ```json
  {
    "code": "兑换码",
    "userToken": "用户token (扩展时长时必填)",
    "customToken": "自定义token (创建用户时可选)"
  }
  ```
- **功能**: 使用兑换码创建用户或扩展时长

### 3. 健康检查
- **URL**: `GET /health`
- **功能**: 服务健康状态检查

## 日志记录功能

### 操作日志 (operation_logs)
记录所有API操作的详细信息：
- 操作类型 (`cdk_use`, `user_create`, `user_extend`, `cdk_validate`, `error`)
- 操作结果 (`success`, `failure`)
- 详细的JSON格式的操作信息
- 请求和响应数据
- 执行时间
- 客户端信息 (IP地址、User-Agent)

### 兑换码用户关系记录 (cdk_user_relations)
记录兑换码与用户的关系：
- 哪个兑换码创建了哪些用户
- 哪个兑换码为哪些用户扩展了时长
- 操作详情和时间记录

## 功能特性

1. **多用户兑换码支持** - 支持单次使用和多次使用的兑换码
2. **自定义token** - 支持8位以上的自定义用户token
3. **详细日志记录** - 完整的操作日志和关系记录
4. **错误处理** - 全面的错误处理和日志记录
5. **数据完整性** - 事务处理和数据一致性保证
6. **模块化设计** - 清晰的分层架构，易于扩展新功能

## 部署说明

### 开发环境
```bash
# 从项目根目录运行
docker-compose up -d
```

### 生产环境
```bash
# 从项目根目录运行
docker-compose -f production-compose.yml up -d
```

### 本地开发
```bash
# 从项目根目录运行
go run ./src
```

## 构建说明

### 本地构建
```bash
# 从项目根目录
go build -o main ./src
```

### Docker构建
```bash
# 从项目根目录
docker build -f src/Dockerfile -t chatsharepanel .
```

## 监控和调试

- **查看实时日志**: `docker logs chatsharepanel-api-1 -f`
- **查看操作日志**: 直接查询 `operation_logs` 表
- **查看关系记录**: 直接查询 `cdk_user_relations` 表
- **健康检查**: 访问 `/health` 端点

## 开发规范

### 目录说明
- `src/` - 所有应用源代码和构建配置
- `sql/` - 数据库相关脚本
- `docs/` - 项目文档
- `config.env` - 环境变量配置（可选）
- `docker-compose.yml` - 开发环境容器编排
- `production-compose.yml` - 生产环境容器编排

### 代码组织原则
- 所有代码文件使用`main`包
- 按功能模块分目录组织
- 保持单一职责原则
- 统一的错误处理和日志记录
- Dockerfile与源代码放在一起 