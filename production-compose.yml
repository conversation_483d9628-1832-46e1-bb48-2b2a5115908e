version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: src/Dockerfile
    ports:
      - "23672:8080"
    environment:
      TZ: Asia/Shanghai
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: cool
      DB_PASSWORD: ahsdu12SAa@#sa1
      DB_NAME: cool
      PORT: 8080
    restart: unless-stopped
    networks:
      - chatgpt-share-shell-copy_default

networks:
  chatgpt-share-shell-copy_default:
    external: true 