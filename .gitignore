# Go编译输出
*.exe
*.exe~
*.dll
*.so
*.dylib

# Go测试相关
*.test
*.out
*.coverage

# Go工作区文件
go.work
go.work.sum

# 依赖目录
vendor/

# 编译后的二进制文件
main
chatsharepanel
api

# 环境变量文件
.env
.env.local
.env.production
.env.development
config.env

# 日志文件
*.log
logs/

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker相关
.dockerignore

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
*.tmp

# 构建产物
build/
dist/
out/

# 文档生成
docs/build/

# 测试覆盖率
coverage.html
coverage.out

# 性能分析文件
*.prof
*.pprof

# 本地开发配置
docker-compose.override.yml
docker-compose.local.yml 