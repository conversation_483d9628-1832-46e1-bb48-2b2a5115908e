services:
  api:
    build:
      context: .
      dockerfile: src/Dockerfile
    ports:
      - "23672:8080"
    environment:
      TZ: Asia/Shanghai
      DB_HOST: chatgpt-share-server-deploy-mysql-1
      DB_PORT: 3306
      DB_USER: cool
      DB_PASSWORD: 123123
      DB_NAME: cool
      PORT: 8080
    restart: unless-stopped
    networks:
      - chatgpt-share-server-deploy_default

networks:
  chatgpt-share-server-deploy_default:
    external: true 