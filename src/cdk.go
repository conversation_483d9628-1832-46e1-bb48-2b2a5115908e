package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// 根据兑换码查询兑换码信息
func getCDKByCode(code string) (*CDKCode, error) {
	var cdk CDKCode
	query := `SELECT id, code, code_type, status, days, is_plus, allow_model, max_uses, used_count, 
			  COALESCE(expire_at, '') as expire_at, COALESCE(used_by_ids, '') as used_by_ids, 
			  created_at, updated_at 
			  FROM cdk_code WHERE code = ?`
	
	row := db.QueryRow(query, code)
	err := row.Scan(&cdk.ID, &cdk.Code, &cdk.CodeType, &cdk.Status, &cdk.Days, 
		&cdk.IsPlus, &cdk.AllowModel, &cdk.MaxUses, &cdk.UsedCount, &cdk.ExpireAt, 
		&cdk.UsedByIDs, &cdk.CreatedAt, &cdk.UpdatedAt)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("兑换码不存在")
		}
		return nil, err
	}
	
	return &cdk, nil
}

// 更新兑换码使用状态
func updateCDKUsage(cdkID int, userID int, usedCount int, usedByIDs string) error {
	// 如果使用次数达到最大值，设置状态为已使用
	status := "unused"
	if usedCount >= getMaxUses(cdkID) {
		status = "used"
	}
	
	query := `UPDATE cdk_code SET 
			  status = ?, 
			  used_count = ?, 
			  used_by_ids = ?, 
			  updated_at = NOW() 
			  WHERE id = ?`
	
	_, err := db.Exec(query, status, usedCount, usedByIDs, cdkID)
	return err
}

// 获取兑换码最大使用次数
func getMaxUses(cdkID int) int {
	var maxUses int
	query := "SELECT max_uses FROM cdk_code WHERE id = ?"
	db.QueryRow(query, cdkID).Scan(&maxUses)
	return maxUses
}

// 检查用户是否已使用过该兑换码
func hasUserUsedCDK(usedByIDs string, userID int) bool {
	if usedByIDs == "" {
		return false
	}
	
	var userIDs []int
	json.Unmarshal([]byte(usedByIDs), &userIDs)
	
	for _, id := range userIDs {
		if id == userID {
			return true
		}
	}
	return false
}

// 添加用户到已使用列表
func addUserToCDK(usedByIDs string, userID int) (string, error) {
	var userIDs []int
	
	if usedByIDs != "" {
		err := json.Unmarshal([]byte(usedByIDs), &userIDs)
		if err != nil {
			return "", err
		}
	}
	
	userIDs = append(userIDs, userID)
	
	result, err := json.Marshal(userIDs)
	if err != nil {
		return "", err
	}
	
	return string(result), nil
}

// API处理函数：使用兑换码
func useCDK(c *gin.Context) {
	startTime := time.Now()
	
	var req CDKUseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		LogValidationError(c, "cdk_use_bind_json", "请求参数错误: "+err.Error(), req)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误",
			Data:    nil,
		})
		return
	}

	// 记录请求开始
	log.Printf("CDK使用请求开始: code=%s, userToken=%s, customToken=%s", 
		req.Code, maskToken(req.UserToken), maskToken(req.CustomToken))
	
	// 查询兑换码信息
	cdk, err := getCDKByCode(req.Code)
	if err != nil {
		executionTime := time.Since(startTime)
		LogCDKUseFailure(c, req.Code, err.Error(), req, executionTime, nil)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: err.Error(),
			Data:    nil,
		})
		return
	}
	
	// 检查兑换码状态
	if cdk.Status == "expired" {
		executionTime := time.Since(startTime)
		LogCDKUseFailure(c, req.Code, "兑换码已过期", req, executionTime, cdk)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "兑换码已过期",
			Data:    nil,
		})
		return
	}
	
	// 检查使用次数
	if cdk.UsedCount >= cdk.MaxUses {
		executionTime := time.Since(startTime)
		LogCDKUseFailure(c, req.Code, "兑换码使用次数已达上限", req, executionTime, cdk)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "兑换码使用次数已达上限",
			Data:    nil,
		})
		return
	}
	
	// 检查兑换码是否过期（时间检查）
	if cdk.ExpireAt != "" {
		// 这里应该添加时间比较逻辑
		// 为了简化，暂时跳过过期检查
	}
	
	var user *User
	var message string
	var oldExpireTime string
	
	if cdk.CodeType == "create_user" {
		// 创建用户模式
		if req.UserToken != "" {
			executionTime := time.Since(startTime)
			LogCDKUseFailure(c, req.Code, "创建用户模式不需要提供userToken", req, executionTime, cdk)
			c.JSON(http.StatusBadRequest, APIResponse{
				Code:    400,
				Message: "创建用户模式不需要提供userToken",
				Data:    nil,
			})
			return
		}
		
		// 确定要使用的token
		var newToken string
		if req.CustomToken != "" {
			// 验证自定义token
			if err := validateCustomToken(req.CustomToken); err != nil {
				LogCDKUseFailure(c, req.Code, err.Error(), req, time.Since(startTime), cdk)
				c.JSON(http.StatusBadRequest, APIResponse{
					Code:    400,
					Message: err.Error(),
					Data:    nil,
				})
				return
			}
			newToken = req.CustomToken
		} else {
			newToken = generateToken()
		}
		
		// 创建用户
		user, err = createUser(newToken, cdk.Days, cdk.IsPlus, cdk.AllowModel, "兑换码创建用户")
		if err != nil {
			LogSystemError(c, "create_user", "创建用户失败: "+err.Error(), map[string]interface{}{
				"token": newToken,
				"cdk":   cdk,
			})
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "创建用户失败",
				Data:    nil,
			})
			return
		}
		
		// 记录用户创建日志
		LogUserCreateSuccess(c, user, true, cdk.Code)
		
		message = "用户创建成功"
		
	} else if cdk.CodeType == "extend_time" {
		// 增加时长模式
		if req.UserToken == "" {
			LogCDKUseFailure(c, req.Code, "增加时长模式需要提供userToken", req, time.Since(startTime), cdk)
			c.JSON(http.StatusBadRequest, APIResponse{
				Code:    400,
				Message: "增加时长模式需要提供userToken",
				Data:    nil,
			})
			return
		}
		
		// 查询现有用户
		user, err = getUserByToken(req.UserToken)
		if err != nil {
			LogCDKUseFailure(c, req.Code, "用户不存在", req, time.Since(startTime), cdk)
			c.JSON(http.StatusNotFound, APIResponse{
				Code:    404,
				Message: "用户不存在",
				Data:    nil,
			})
			return
		}
		
		// 检查用户是否已使用过该兑换码
		if hasUserUsedCDK(cdk.UsedByIDs, user.ID) {
			LogCDKUseFailure(c, req.Code, "该用户已使用过此兑换码", req, time.Since(startTime), cdk)
			c.JSON(http.StatusBadRequest, APIResponse{
				Code:    400,
				Message: "该用户已使用过此兑换码",
				Data:    nil,
			})
			return
		}
		
		// 保存旧的过期时间
		oldExpireTime = user.ExpireTime
		
		// 更新用户时长
		err = updateUserTime(user.ID, cdk.Days, cdk.IsPlus, cdk.AllowModel)
		if err != nil {
			LogSystemError(c, "update_user_time", "更新用户信息失败: "+err.Error(), map[string]interface{}{
				"userId":     user.ID,
				"cdk":        cdk,
				"oldExpire":  oldExpireTime,
			})
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "更新用户信息失败",
				Data:    nil,
			})
			return
		}
		
		// 重新查询用户信息
		user, err = getUserByToken(req.UserToken)
		if err != nil {
			LogSystemError(c, "get_user_after_update", "获取用户信息失败: "+err.Error(), map[string]interface{}{
				"userToken": maskToken(req.UserToken),
			})
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "获取用户信息失败",
				Data:    nil,
			})
			return
		}
		
		// 记录用户时长扩展日志
		LogUserExtendSuccess(c, user, cdk, oldExpireTime)
		
		message = "时长增加成功"
	} else {
		LogCDKUseFailure(c, req.Code, "未知的兑换码类型: "+cdk.CodeType, req, time.Since(startTime), cdk)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "未知的兑换码类型",
			Data:    nil,
		})
		return
	}
	
	// 更新兑换码使用状态
	newUsedByIDs, err := addUserToCDK(cdk.UsedByIDs, user.ID)
	if err != nil {
		LogSystemError(c, "add_user_to_cdk", "更新兑换码状态失败: "+err.Error(), map[string]interface{}{
			"cdkId":      cdk.ID,
			"userId":     user.ID,
			"usedByIDs":  cdk.UsedByIDs,
		})
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "更新兑换码状态失败",
			Data:    nil,
		})
		return
	}
	
	err = updateCDKUsage(cdk.ID, user.ID, cdk.UsedCount+1, newUsedByIDs)
	if err != nil {
		LogSystemError(c, "update_cdk_usage", "更新兑换码状态失败: "+err.Error(), map[string]interface{}{
			"cdkId":       cdk.ID,
			"userId":      user.ID,
			"newUsedCount": cdk.UsedCount + 1,
			"newUsedByIDs": newUsedByIDs,
		})
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "更新兑换码状态失败",
			Data:    nil,
		})
		return
	}
	
	// 计算执行时间
	executionTime := time.Since(startTime)
	
	// 记录成功日志
	LogCDKUseSuccess(c, cdk, user, req, message, executionTime)
	
	response := CDKUseResponse{
		UserToken:  user.UserToken,
		ExpireTime: user.ExpireTime,
		IsPlus:     user.IsPlus,
		AllowModel: user.AllowModel,
		Count:      user.Count,
		Message:    message,
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "兑换码使用成功",
		Data:    response,
	})
}

// 辅助函数：掩码token显示
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
} 