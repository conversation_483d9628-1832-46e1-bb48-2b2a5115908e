# 使用官方Go镜像作为构建环境
FROM golang:1.19-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY src/ ./

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

# 使用alpine作为运行环境
FROM alpine:latest

# 安装ca证书
RUN apk --no-cache add ca-certificates

# 创建工作目录
WORKDIR /root/

# 从builder阶段复制可执行文件
COPY --from=builder /app/main .

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"] 