package main

import (
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// JWT密钥
var jwtSecret = []byte("your-secret-key-change-this-in-production")

// JWT Claims
type Claims struct {
	AdminID  int    `json:"admin_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// 管理员登录
func AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		LogOperationFailure("admin_login", "请求参数错误", err.Error(), c)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 计算密码哈希
	hash := sha256.Sum256([]byte(req.Password))
	passwordHash := hex.EncodeToString(hash[:])

	// 查询管理员用户
	var admin AdminUser
	query := `
		SELECT id, username, password_hash, real_name, email, status, 
			   last_login_at, login_count, created_at, updated_at
		FROM admin_users 
		WHERE username = ? AND password_hash = ? AND status = 'active'
	`
	
	err := db.QueryRow(query, req.Username, passwordHash).Scan(
		&admin.ID, &admin.Username, &admin.PasswordHash, &admin.RealName, 
		&admin.Email, &admin.Status, &admin.LastLoginAt, &admin.LoginCount,
		&admin.CreatedAt, &admin.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			LogOperationFailure("admin_login", "用户名或密码错误", "用户名: " + req.Username, c)
			c.JSON(http.StatusUnauthorized, APIResponse{
				Code:    401,
				Message: "用户名或密码错误",
			})
			return
		}
		LogOperationFailure("admin_login", "查询管理员失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}

	// 更新最后登录时间和登录次数
	updateQuery := `
		UPDATE admin_users 
		SET last_login_at = NOW(), login_count = login_count + 1
		WHERE id = ?
	`
	_, err = db.Exec(updateQuery, admin.ID)
	if err != nil {
		fmt.Printf("更新登录信息失败: %v\n", err)
	}

	// 生成JWT Token
	claims := Claims{
		AdminID:  admin.ID,
		Username: admin.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // 24小时过期
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		LogOperationFailure("admin_login", "生成Token失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}

	LogOperationSuccess("admin_login", "管理员登录成功", map[string]interface{}{
		"admin_id": admin.ID,
		"username": admin.Username,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "登录成功",
		Data: AdminLoginResponse{
			Token:     tokenString,
			AdminUser: admin,
		},
	})
}

// JWT中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, APIResponse{
				Code:    401,
				Message: "未提供认证信息",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, APIResponse{
				Code:    401,
				Message: "认证格式错误",
			})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		
		// 解析JWT Token
		token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
			return jwtSecret, nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, APIResponse{
				Code:    401,
				Message: "Token无效",
			})
			c.Abort()
			return
		}

		if claims, ok := token.Claims.(*Claims); ok {
			c.Set("admin_id", claims.AdminID)
			c.Set("username", claims.Username)
		}

		c.Next()
	}
}

// 获取CDK列表（管理员）
func AdminGetCDKList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	status := c.Query("status")
	codeType := c.Query("codeType")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询条件
	whereConditions := []string{}
	args := []interface{}{}

	if status != "" {
		whereConditions = append(whereConditions, "status = ?")
		args = append(args, status)
	}
	if codeType != "" {
		whereConditions = append(whereConditions, "code_type = ?")
		args = append(args, codeType)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM cdk_codes " + whereClause
	var total int
	err := db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		LogOperationFailure("admin_get_cdk_list", "查询CDK总数失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT id, code, code_type, status, days, is_plus, allow_model, 
			   max_uses, used_count, expire_at, used_by_ids, created_at, updated_at
		FROM cdk_codes ` + whereClause + ` 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?
	`
	
	dataArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, dataArgs...)
	if err != nil {
		LogOperationFailure("admin_get_cdk_list", "查询CDK列表失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}
	defer rows.Close()

	var cdkList []CDKCode
	for rows.Next() {
		var cdk CDKCode
		err := rows.Scan(
			&cdk.ID, &cdk.Code, &cdk.CodeType, &cdk.Status, &cdk.Days,
			&cdk.IsPlus, &cdk.AllowModel, &cdk.MaxUses, &cdk.UsedCount,
			&cdk.ExpireAt, &cdk.UsedByIDs, &cdk.CreatedAt, &cdk.UpdatedAt,
		)
		if err != nil {
			LogOperationFailure("admin_get_cdk_list", "解析CDK数据失败", err.Error(), c)
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "系统错误",
			})
			return
		}
		cdkList = append(cdkList, cdk)
	}

	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	
	LogOperationSuccess("admin_get_cdk_list", "获取CDK列表成功", map[string]interface{}{
		"total": total,
		"page":  page,
		"pageSize": pageSize,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "success",
		Data: PaginationResponse{
			Total:       total,
			CurrentPage: page,
			PageSize:    pageSize,
			TotalPages:  totalPages,
			Data:        cdkList,
		},
	})
}

// 创建CDK
func AdminCreateCDK(c *gin.Context) {
	var req CDKCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		LogOperationFailure("admin_create_cdk", "请求参数错误", err.Error(), c)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 检查兑换码是否已存在
	var existingID int
	checkQuery := "SELECT id FROM cdk_codes WHERE code = ?"
	err := db.QueryRow(checkQuery, req.Code).Scan(&existingID)
	if err == nil {
		LogOperationFailure("admin_create_cdk", "兑换码已存在", "code: " + req.Code, c)
		c.JSON(http.StatusConflict, APIResponse{
			Code:    409,
			Message: "兑换码已存在",
		})
		return
	}

	// 插入新的兑换码
	insertQuery := `
		INSERT INTO cdk_codes (code, code_type, days, is_plus, allow_model, max_uses, expire_at, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
	`
	
	expireAt := req.ExpireAt
	if expireAt == "" {
		// 默认30天后过期
		expireAt = time.Now().AddDate(0, 0, 30).Format("2006-01-02 15:04:05")
	}

	result, err := db.Exec(insertQuery, req.Code, req.CodeType, req.Days, req.IsPlus, req.AllowModel, req.MaxUses, expireAt)
	if err != nil {
		LogOperationFailure("admin_create_cdk", "创建CDK失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "创建失败",
		})
		return
	}

	cdkID, _ := result.LastInsertId()
	
	LogOperationSuccess("admin_create_cdk", "创建CDK成功", map[string]interface{}{
		"cdk_id": cdkID,
		"code":   req.Code,
		"type":   req.CodeType,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "创建成功",
		Data: map[string]interface{}{
			"id": cdkID,
		},
	})
}

// 更新CDK
func AdminUpdateCDK(c *gin.Context) {
	cdkID := c.Param("id")
	
	var req CDKUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		LogOperationFailure("admin_update_cdk", "请求参数错误", err.Error(), c)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 检查CDK是否存在
	var existingCode string
	checkQuery := "SELECT code FROM cdk_codes WHERE id = ?"
	err := db.QueryRow(checkQuery, cdkID).Scan(&existingCode)
	if err == sql.ErrNoRows {
		LogOperationFailure("admin_update_cdk", "CDK不存在", "id: " + cdkID, c)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "CDK不存在",
		})
		return
	}

	// 更新CDK
	updateQuery := `
		UPDATE cdk_codes 
		SET code_type = ?, days = ?, is_plus = ?, allow_model = ?, max_uses = ?, expire_at = ?, status = ?
		WHERE id = ?
	`
	
	expireAt := req.ExpireAt
	if expireAt == "" {
		expireAt = time.Now().AddDate(0, 0, 30).Format("2006-01-02 15:04:05")
	}

	_, err = db.Exec(updateQuery, req.CodeType, req.Days, req.IsPlus, req.AllowModel, req.MaxUses, expireAt, req.Status, cdkID)
	if err != nil {
		LogOperationFailure("admin_update_cdk", "更新CDK失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "更新失败",
		})
		return
	}

	LogOperationSuccess("admin_update_cdk", "更新CDK成功", map[string]interface{}{
		"cdk_id": cdkID,
		"code":   existingCode,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "更新成功",
	})
}

// 删除CDK（软删除）
func AdminDeleteCDK(c *gin.Context) {
	cdkID := c.Param("id")

	// 检查CDK是否存在
	var existingCode string
	checkQuery := "SELECT code FROM cdk_codes WHERE id = ?"
	err := db.QueryRow(checkQuery, cdkID).Scan(&existingCode)
	if err == sql.ErrNoRows {
		LogOperationFailure("admin_delete_cdk", "CDK不存在", "id: " + cdkID, c)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "CDK不存在",
		})
		return
	}

	// 软删除CDK
	deleteQuery := "UPDATE cdk_codes SET status = 'deleted' WHERE id = ?"
	_, err = db.Exec(deleteQuery, cdkID)
	if err != nil {
		LogOperationFailure("admin_delete_cdk", "删除CDK失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "删除失败",
		})
		return
	}

	LogOperationSuccess("admin_delete_cdk", "删除CDK成功", map[string]interface{}{
		"cdk_id": cdkID,
		"code":   existingCode,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "删除成功",
	})
}

// 获取用户列表（管理员）
func AdminGetUserList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	isPlus := c.Query("isPlus")
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询条件
	whereConditions := []string{}
	args := []interface{}{}

	if isPlus != "" {
		whereConditions = append(whereConditions, "is_plus = ?")
		args = append(args, isPlus == "true")
	}
	if search != "" {
		whereConditions = append(whereConditions, "(userToken LIKE ? OR remark LIKE ?)")
		args = append(args, "%"+search+"%", "%"+search+"%")
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM users " + whereClause
	var total int
	err := db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		LogOperationFailure("admin_get_user_list", "查询用户总数失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT id, userToken, expire_time, is_plus, remark, allow_model, count, create_time, update_time
		FROM users ` + whereClause + ` 
		ORDER BY create_time DESC 
		LIMIT ? OFFSET ?
	`
	
	dataArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, dataArgs...)
	if err != nil {
		LogOperationFailure("admin_get_user_list", "查询用户列表失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}
	defer rows.Close()

	var userList []User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.UserToken, &user.ExpireTime, &user.IsPlus,
			&user.Remark, &user.AllowModel, &user.Count, &user.CreateTime, &user.UpdateTime,
		)
		if err != nil {
			LogOperationFailure("admin_get_user_list", "解析用户数据失败", err.Error(), c)
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "系统错误",
			})
			return
		}
		userList = append(userList, user)
	}

	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	
	LogOperationSuccess("admin_get_user_list", "获取用户列表成功", map[string]interface{}{
		"total": total,
		"page":  page,
		"pageSize": pageSize,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "success",
		Data: PaginationResponse{
			Total:       total,
			CurrentPage: page,
			PageSize:    pageSize,
			TotalPages:  totalPages,
			Data:        userList,
		},
	})
}

// 获取操作日志列表（管理员）
func AdminGetLogList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	operationType := c.Query("operationType")
	result := c.Query("result")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询条件
	whereConditions := []string{}
	args := []interface{}{}

	if operationType != "" {
		whereConditions = append(whereConditions, "operation_type = ?")
		args = append(args, operationType)
	}
	if result != "" {
		whereConditions = append(whereConditions, "result = ?")
		args = append(args, result)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM operation_logs " + whereClause
	var total int
	err := db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		LogOperationFailure("admin_get_log_list", "查询日志总数失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT id, operation_type, result, message, details, ip_address, user_agent, 
			   execution_time, created_at
		FROM operation_logs ` + whereClause + ` 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?
	`
	
	dataArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, dataArgs...)
	if err != nil {
		LogOperationFailure("admin_get_log_list", "查询日志列表失败", err.Error(), c)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "系统错误",
		})
		return
	}
	defer rows.Close()

	var logList []map[string]interface{}
	for rows.Next() {
		var log map[string]interface{} = make(map[string]interface{})
		var id, operationType, result, message, details, ipAddress, userAgent, executionTime, createdAt string
		
		err := rows.Scan(&id, &operationType, &result, &message, &details, &ipAddress, &userAgent, &executionTime, &createdAt)
		if err != nil {
			LogOperationFailure("admin_get_log_list", "解析日志数据失败", err.Error(), c)
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "系统错误",
			})
			return
		}
		
		log["id"] = id
		log["operationType"] = operationType
		log["result"] = result
		log["message"] = message
		log["details"] = details
		log["ipAddress"] = ipAddress
		log["userAgent"] = userAgent
		log["executionTime"] = executionTime
		log["createdAt"] = createdAt
		
		logList = append(logList, log)
	}

	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	
	LogOperationSuccess("admin_get_log_list", "获取日志列表成功", map[string]interface{}{
		"total": total,
		"page":  page,
		"pageSize": pageSize,
	}, c)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "success",
		Data: PaginationResponse{
			Total:       total,
			CurrentPage: page,
			PageSize:    pageSize,
			TotalPages:  totalPages,
			Data:        logList,
		},
	})
} 