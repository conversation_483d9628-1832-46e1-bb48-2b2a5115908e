package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// 根据token查询用户信息
func getUserByToken(token string) (*User, error) {
	var user User
	query := "SELECT id, userToken, expireTime, isPlus, COALESCE(remark, '') as remark, COALESCE(allowModel, 'all') as allowModel, COALESCE(count, 0) as count, createTime, updateTime FROM chatgpt_user WHERE userToken = ? AND deleted_at IS NULL"
	
	log.Printf("查询用户信息: token=%s", maskToken(token))
	
	row := db.QueryRow(query, token)
	err := row.Scan(&user.ID, &user.UserToken, &user.ExpireTime, &user.IsPlus, &user.Remark, &user.AllowModel, &user.Count, &user.CreateTime, &user.UpdateTime)
	
	if err != nil {
		log.Printf("数据库查询错误: %v, token=%s", err, maskToken(token))
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, err
	}
	
	log.Printf("成功找到用户: ID=%d, token=%s", user.ID, maskToken(token))
	return &user, nil
}

// 创建新用户
func createUser(token string, days int, isPlus bool, allowModel string, remark string) (*User, error) {
	expireTime := time.Now().AddDate(0, 0, days)
	
	log.Printf("创建新用户: token=%s, days=%d, isPlus=%t, allowModel=%s", 
		maskToken(token), days, isPlus, allowModel)
	
	query := `INSERT INTO chatgpt_user (userToken, expireTime, isPlus, allowModel, remark, createTime, updateTime) 
			  VALUES (?, ?, ?, ?, ?, NOW(), NOW())`
	
	result, err := db.Exec(query, token, expireTime, isPlus, allowModel, remark)
	if err != nil {
		log.Printf("创建用户失败: %v, token=%s", err, maskToken(token))
		return nil, err
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		log.Printf("获取新用户ID失败: %v", err)
		return nil, err
	}
	
	log.Printf("用户创建成功: ID=%d, token=%s", id, maskToken(token))
	
	// 返回创建的用户信息
	return &User{
		ID:         int(id),
		UserToken:  token,
		ExpireTime: expireTime.Format("2006-01-02T15:04:05Z"),
		IsPlus:     isPlus,
		AllowModel: allowModel,
		Count:      0,
		Remark:     remark,
	}, nil
}

// 更新用户时长和其他信息
func updateUserTime(userID int, additionalDays int, isPlus bool, allowModel string) error {
	log.Printf("更新用户时长: userID=%d, additionalDays=%d, isPlus=%t, allowModel=%s", 
		userID, additionalDays, isPlus, allowModel)
	
	query := `UPDATE chatgpt_user SET 
			  expireTime = DATE_ADD(expireTime, INTERVAL ? DAY),
			  isPlus = ?,
			  allowModel = ?,
			  updateTime = NOW()
			  WHERE id = ?`
	
	result, err := db.Exec(query, additionalDays, isPlus, allowModel, userID)
	if err != nil {
		log.Printf("更新用户时长失败: %v, userID=%d", err, userID)
		return err
	}
	
	rowsAffected, _ := result.RowsAffected()
	log.Printf("用户时长更新成功: userID=%d, rowsAffected=%d", userID, rowsAffected)
	
	return nil
}

// 验证自定义token
func validateCustomToken(token string) error {
	if len(token) < 8 {
		return fmt.Errorf("自定义token长度必须至少8位")
	}
	
	// 检查token是否已存在
	var count int
	query := "SELECT COUNT(*) FROM chatgpt_user WHERE userToken = ? AND deleted_at IS NULL"
	err := db.QueryRow(query, token).Scan(&count)
	if err != nil {
		log.Printf("验证token时数据库错误: %v, token=%s", err, maskToken(token))
		return err
	}
	
	if count > 0 {
		log.Printf("token已存在: %s", maskToken(token))
		return fmt.Errorf("token已存在")
	}
	
	log.Printf("token验证通过: %s", maskToken(token))
	return nil
}

// API处理函数：获取用户信息
func getUserInfo(c *gin.Context) {
	startTime := time.Now()
	token := c.Query("token")
	
	if token == "" {
		LogValidationError(c, "get_user_info", "Token参数缺失", map[string]interface{}{
			"queryParams": c.Request.URL.Query(),
		})
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Token is required",
			Data:    nil,
		})
		return
	}
	
	log.Printf("获取用户信息请求: token=%s", maskToken(token))
	
	user, err := getUserByToken(token)
	if err != nil {
		executionTime := time.Since(startTime)
		
		if err.Error() == "user not found" {
			LogOperation(c, OperationLog{
				OperationType:   "get_user_info",
				UserToken:       maskToken(token),
				OperationResult: ResultFailure,
				ErrorMessage:    "用户不存在",
				RequestData:     map[string]string{"token": maskToken(token)},
				ExecutionTimeMs: executionTime.Milliseconds(),
			})
			c.JSON(http.StatusNotFound, APIResponse{
				Code:    404,
				Message: "User not found",
				Data:    nil,
			})
			return
		}
		
		LogSystemError(c, "get_user_info", "获取用户信息失败: "+err.Error(), map[string]interface{}{
			"token": maskToken(token),
		})
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "Internal server error",
			Data:    nil,
		})
		return
	}
	
	executionTime := time.Since(startTime)
	
	// 移除敏感信息
	responseUser := *user
	responseUser.UserToken = ""
	
	// 记录成功日志
	LogOperation(c, OperationLog{
		OperationType:   "get_user_info",
		UserID:          user.ID,
		UserToken:       maskToken(token),
		OperationResult: ResultSuccess,
		OperationDetails: map[string]interface{}{
			"userId":     user.ID,
			"isPlus":     user.IsPlus,
			"allowModel": user.AllowModel,
			"expireTime": user.ExpireTime,
		},
		RequestData:     map[string]string{"token": maskToken(token)},
		ResponseData:    responseUser,
		ExecutionTimeMs: executionTime.Milliseconds(),
	})
	
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Success",
		Data:    responseUser,
	})
} 