package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
)

var db *sql.DB

// 初始化数据库连接
func initDB() {
	var err error
	
	// 从环境变量获取数据库配置
	dbHost := getEnv("DB_HOST", "chatgpt-share-server-deploy-mysql-1")
	dbPort := getEnv("DB_PORT", "3306")
	dbUser := getEnv("DB_USER", "cool")
	dbPassword := getEnv("DB_PASSWORD", "123123")
	dbName := getEnv("DB_NAME", "cool")
	
	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)
	
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	
	// 测试数据库连接
	if err = db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}
	
	log.Println("Database connected successfully")
}

// 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 加载环境变量
func loadEnv() {
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}
} 