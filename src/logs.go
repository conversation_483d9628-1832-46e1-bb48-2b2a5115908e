package main

import (
	"encoding/json"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// 操作类型常量
const (
	OperationCDKUse      = "cdk_use"
	OperationUserCreate  = "user_create"
	OperationUserExtend  = "user_extend"
	OperationCDKValidate = "cdk_validate"
	OperationError       = "error"
)

// 操作结果常量
const (
	ResultSuccess = "success"
	ResultFailure = "failure"
)

// 关系类型常量
const (
	RelationCreate = "create"
	RelationExtend = "extend"
)

// 操作日志结构体
type OperationLog struct {
	OperationType    string      `json:"operationType"`
	CDKCode          string      `json:"cdkCode,omitempty"`
	CDKID            int         `json:"cdkId,omitempty"`
	UserID           int         `json:"userId,omitempty"`
	UserToken        string      `json:"userToken,omitempty"`
	OperationResult  string      `json:"operationResult"`
	OperationDetails interface{} `json:"operationDetails,omitempty"`
	ErrorMessage     string      `json:"errorMessage,omitempty"`
	IPAddress        string      `json:"ipAddress,omitempty"`
	UserAgent        string      `json:"userAgent,omitempty"`
	RequestData      interface{} `json:"requestData,omitempty"`
	ResponseData     interface{} `json:"responseData,omitempty"`
	ExecutionTimeMs  int64       `json:"executionTimeMs,omitempty"`
}

// 兑换码用户关系记录结构体
type CDKUserRelation struct {
	CDKID            int         `json:"cdkId"`
	CDKCode          string      `json:"cdkCode"`
	UserID           int         `json:"userId"`
	UserToken        string      `json:"userToken"`
	RelationType     string      `json:"relationType"`
	OperationDetails interface{} `json:"operationDetails,omitempty"`
}

// 记录操作日志
func LogOperation(c *gin.Context, logData OperationLog) {
	// 获取客户端信息
	if c != nil {
		logData.IPAddress = c.ClientIP()
		logData.UserAgent = c.GetHeader("User-Agent")
	}

	// 转换为JSON
	operationDetailsJSON, _ := json.Marshal(logData.OperationDetails)
	requestDataJSON, _ := json.Marshal(logData.RequestData)
	responseDataJSON, _ := json.Marshal(logData.ResponseData)

	query := `INSERT INTO operation_logs 
			  (operation_type, cdk_code, cdk_id, user_id, user_token, operation_result, 
			   operation_details, error_message, ip_address, user_agent, request_data, 
			   response_data, execution_time_ms) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := db.Exec(query,
		logData.OperationType,
		nullString(logData.CDKCode),
		nullInt(logData.CDKID),
		nullInt(logData.UserID),
		nullString(logData.UserToken),
		logData.OperationResult,
		nullString(string(operationDetailsJSON)),
		nullString(logData.ErrorMessage),
		nullString(logData.IPAddress),
		nullString(logData.UserAgent),
		nullString(string(requestDataJSON)),
		nullString(string(responseDataJSON)),
		nullInt64(logData.ExecutionTimeMs),
	)

	if err != nil {
		log.Printf("Failed to log operation: %v", err)
	}
}

// 记录兑换码用户关系
func LogCDKUserRelation(relation CDKUserRelation) {
	operationDetailsJSON, _ := json.Marshal(relation.OperationDetails)

	query := `INSERT INTO cdk_user_relations 
			  (cdk_id, cdk_code, user_id, user_token, relation_type, operation_details) 
			  VALUES (?, ?, ?, ?, ?, ?)
			  ON DUPLICATE KEY UPDATE 
			  operation_details = VALUES(operation_details), 
			  created_at = NOW()`

	_, err := db.Exec(query,
		relation.CDKID,
		relation.CDKCode,
		relation.UserID,
		relation.UserToken,
		relation.RelationType,
		string(operationDetailsJSON),
	)

	if err != nil {
		log.Printf("Failed to log CDK user relation: %v", err)
	}
}

// 记录兑换码使用成功日志
func LogCDKUseSuccess(c *gin.Context, cdk *CDKCode, user *User, req CDKUseRequest, message string, executionTime time.Duration) {
	details := map[string]interface{}{
		"message":      message,
		"cdkType":      cdk.CodeType,
		"days":         cdk.Days,
		"isPlus":       cdk.IsPlus,
		"allowModel":   cdk.AllowModel,
		"maxUses":      cdk.MaxUses,
		"usedCount":    cdk.UsedCount + 1,
		"newUserInfo": map[string]interface{}{
			"id":         user.ID,
			"expireTime": user.ExpireTime,
			"isPlus":     user.IsPlus,
			"allowModel": user.AllowModel,
		},
	}

	if req.CustomToken != "" {
		details["customToken"] = true
		details["customTokenValue"] = req.CustomToken
	}

	LogOperation(c, OperationLog{
		OperationType:    OperationCDKUse,
		CDKCode:          cdk.Code,
		CDKID:            cdk.ID,
		UserID:           user.ID,
		UserToken:        user.UserToken,
		OperationResult:  ResultSuccess,
		OperationDetails: details,
		RequestData:      req,
		ExecutionTimeMs:  executionTime.Milliseconds(),
	})

	// 记录兑换码用户关系
	relationType := RelationCreate
	if cdk.CodeType == "extend_time" {
		relationType = RelationExtend
	}

	LogCDKUserRelation(CDKUserRelation{
		CDKID:        cdk.ID,
		CDKCode:      cdk.Code,
		UserID:       user.ID,
		UserToken:    user.UserToken,
		RelationType: relationType,
		OperationDetails: map[string]interface{}{
			"operation": message,
			"days":      cdk.Days,
			"isPlus":    cdk.IsPlus,
		},
	})
}

// 记录兑换码使用失败日志
func LogCDKUseFailure(c *gin.Context, cdkCode string, errorMsg string, req CDKUseRequest, executionTime time.Duration, cdk *CDKCode) {
	var cdkID int
	var details map[string]interface{}

	if cdk != nil {
		cdkID = cdk.ID
		details = map[string]interface{}{
			"cdkType":    cdk.CodeType,
			"status":     cdk.Status,
			"maxUses":    cdk.MaxUses,
			"usedCount":  cdk.UsedCount,
			"expireAt":   cdk.ExpireAt,
		}
	}

	LogOperation(c, OperationLog{
		OperationType:    OperationCDKUse,
		CDKCode:          cdkCode,
		CDKID:            cdkID,
		OperationResult:  ResultFailure,
		ErrorMessage:     errorMsg,
		OperationDetails: details,
		RequestData:      req,
		ExecutionTimeMs:  executionTime.Milliseconds(),
	})
}

// 记录用户创建日志
func LogUserCreateSuccess(c *gin.Context, user *User, fromCDK bool, cdkCode string) {
	details := map[string]interface{}{
		"fromCDK":     fromCDK,
		"userInfo": map[string]interface{}{
			"id":         user.ID,
			"token":      user.UserToken,
			"expireTime": user.ExpireTime,
			"isPlus":     user.IsPlus,
			"allowModel": user.AllowModel,
			"remark":     user.Remark,
		},
	}

	if fromCDK {
		details["cdkCode"] = cdkCode
	}

	LogOperation(c, OperationLog{
		OperationType:    OperationUserCreate,
		CDKCode:          cdkCode,
		UserID:           user.ID,
		UserToken:        user.UserToken,
		OperationResult:  ResultSuccess,
		OperationDetails: details,
	})
}

// 记录用户时长扩展日志
func LogUserExtendSuccess(c *gin.Context, user *User, cdk *CDKCode, oldExpireTime string) {
	details := map[string]interface{}{
		"userId":         user.ID,
		"oldExpireTime":  oldExpireTime,
		"newExpireTime":  user.ExpireTime,
		"extendedDays":   cdk.Days,
		"newIsPlus":      user.IsPlus,
		"newAllowModel":  user.AllowModel,
	}

	LogOperation(c, OperationLog{
		OperationType:    OperationUserExtend,
		CDKCode:          cdk.Code,
		CDKID:            cdk.ID,
		UserID:           user.ID,
		UserToken:        user.UserToken,
		OperationResult:  ResultSuccess,
		OperationDetails: details,
	})
}

// 记录验证错误日志
func LogValidationError(c *gin.Context, operation string, errorMsg string, requestData interface{}) {
	LogOperation(c, OperationLog{
		OperationType:   OperationError,
		OperationResult: ResultFailure,
		ErrorMessage:    errorMsg,
		OperationDetails: map[string]interface{}{
			"operation": operation,
		},
		RequestData: requestData,
	})
}

// 记录系统错误日志
func LogSystemError(c *gin.Context, operation string, errorMsg string, details interface{}) {
	LogOperation(c, OperationLog{
		OperationType:    OperationError,
		OperationResult:  ResultFailure,
		ErrorMessage:     errorMsg,
		OperationDetails: details,
	})
}

// 辅助函数：处理NULL字符串
func nullString(s string) interface{} {
	if s == "" {
		return nil
	}
	return s
}

// 辅助函数：处理NULL整数
func nullInt(i int) interface{} {
	if i == 0 {
		return nil
	}
	return i
}

// 辅助函数：处理NULL int64
func nullInt64(i int64) interface{} {
	if i == 0 {
		return nil
	}
	return i
} 