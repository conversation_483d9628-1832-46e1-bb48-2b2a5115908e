package main

// User 定义用户结构体
type User struct {
	ID          int    `json:"id"`
	UserToken   string `json:"userToken"`
	ExpireTime  string `json:"expireTime"`
	IsPlus      bool   `json:"isPlus"`
	Remark      string `json:"remark"`
	AllowModel  string `json:"allowModel"`
	Count       int    `json:"count"`
	CreateTime  string `json:"createTime"`
	UpdateTime  string `json:"updateTime"`
}

// CDKCode 定义兑换码结构体
type CDKCode struct {
	ID          int    `json:"id"`
	Code        string `json:"code"`
	CodeType    string `json:"codeType"`
	Status      string `json:"status"`
	Days        int    `json:"days"`
	IsPlus      bool   `json:"isPlus"`
	AllowModel  string `json:"allowModel"`
	MaxUses     int    `json:"maxUses"`
	UsedCount   int    `json:"usedCount"`
	ExpireAt    string `json:"expireAt"`
	UsedByIDs   string `json:"usedByIds"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// CDKUseRequest 兑换码使用请求
type CDKUseRequest struct {
	Code        string `json:"code" binding:"required"`
	UserToken   string `json:"userToken,omitempty"`
	CustomToken string `json:"customToken,omitempty"`
}

// CDKUseResponse 兑换码使用响应
type CDKUseResponse struct {
	UserToken   string `json:"userToken"`
	ExpireTime  string `json:"expireTime"`
	IsPlus      bool   `json:"isPlus"`
	AllowModel  string `json:"allowModel"`
	Count       int    `json:"count"`
	Message     string `json:"message"`
}

// APIResponse 定义API响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
} 