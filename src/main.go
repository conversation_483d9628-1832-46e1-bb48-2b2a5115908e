package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载环境变量
	loadEnv()
	
	// 初始化数据库
	initDB()
	defer db.Close()
	
	// 创建Gin路由器
	r := gin.Default()
	
	// 设置路由
	r.GET("/api/user/info", getUserInfo)
	r.POST("/api/cdk/use", useCDK)
	
	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"message": "Service is running",
		})
	})
	
	// 启动服务器
	port := getEnv("PORT", "8080")
	log.Printf("Server starting on port %s", port)
	
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
} 