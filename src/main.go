package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
)

// 全局变量
var db *sql.DB

// User 定义用户结构体
type User struct {
	ID          int    `json:"id"`
	UserToken   string `json:"userToken"`
	ExpireTime  string `json:"expireTime"`
	IsPlus      bool   `json:"isPlus"`
	Remark      string `json:"remark"`
	AllowModel  string `json:"allowModel"`
	Count       int    `json:"count"`
	CreateTime  string `json:"createTime"`
	UpdateTime  string `json:"updateTime"`
}

// CDKCode 定义兑换码结构体
type CDKCode struct {
	ID          int    `json:"id"`
	Code        string `json:"code"`
	CodeType    string `json:"codeType"`
	Status      string `json:"status"`
	Days        int    `json:"days"`
	IsPlus      bool   `json:"isPlus"`
	AllowModel  string `json:"allowModel"`
	MaxUses     int    `json:"maxUses"`
	UsedCount   int    `json:"usedCount"`
	ExpireAt    string `json:"expireAt"`
	UsedByIDs   string `json:"usedByIds"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// CDKUseRequest 兑换码使用请求
type CDKUseRequest struct {
	Code        string `json:"code" binding:"required"`
	UserToken   string `json:"userToken,omitempty"`
	CustomToken string `json:"customToken,omitempty"`
}

// CDKUseResponse 兑换码使用响应
type CDKUseResponse struct {
	UserToken   string `json:"userToken"`
	ExpireTime  string `json:"expireTime"`
	IsPlus      bool   `json:"isPlus"`
	AllowModel  string `json:"allowModel"`
	Count       int    `json:"count"`
	Message     string `json:"message"`
}

// APIResponse 定义API响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 操作类型常量
const (
	OperationCDKUse      = "cdk_use"
	OperationUserCreate  = "user_create"
	OperationUserExtend  = "user_extend"
	OperationCDKValidate = "cdk_validate"
	OperationError       = "error"
)

// 操作结果常量
const (
	ResultSuccess = "success"
	ResultFailure = "failure"
)

// 关系类型常量
const (
	RelationCreate = "create"
	RelationExtend = "extend"
)

// 操作日志结构体
type OperationLog struct {
	OperationType    string      `json:"operationType"`
	CDKCode          string      `json:"cdkCode,omitempty"`
	CDKID            int         `json:"cdkId,omitempty"`
	UserID           int         `json:"userId,omitempty"`
	UserToken        string      `json:"userToken,omitempty"`
	OperationResult  string      `json:"operationResult"`
	OperationDetails interface{} `json:"operationDetails,omitempty"`
	ErrorMessage     string      `json:"errorMessage,omitempty"`
	IPAddress        string      `json:"ipAddress,omitempty"`
	UserAgent        string      `json:"userAgent,omitempty"`
	RequestData      interface{} `json:"requestData,omitempty"`
	ResponseData     interface{} `json:"responseData,omitempty"`
	ExecutionTimeMs  int64       `json:"executionTimeMs,omitempty"`
}

// 兑换码用户关系记录结构体
type CDKUserRelation struct {
	CDKID            int         `json:"cdkId"`
	CDKCode          string      `json:"cdkCode"`
	UserID           int         `json:"userId"`
	UserToken        string      `json:"userToken"`
	RelationType     string      `json:"relationType"`
	OperationDetails interface{} `json:"operationDetails,omitempty"`
}

func main() {
	// 加载环境变量
	loadEnv()
	
	// 初始化数据库
	initDB()
	defer db.Close()
	
	// 创建Gin路由器
	r := gin.Default()
	
	// 设置路由
	r.GET("/api/user/info", getUserInfo)
	r.POST("/api/cdk/use", useCDK)
	
	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"message": "Service is running",
		})
	})
	
	// 启动服务器
	port := getEnv("PORT", "8080")
	log.Printf("Server starting on port %s", port)
	
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
} 