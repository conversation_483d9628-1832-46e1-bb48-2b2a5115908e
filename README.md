# ChatShare Panel API

一个Go API项目，连接到ChatGPT Share Server的MySQL数据库，提供用户信息查询接口。

## 功能特性

- 连接到现有的MySQL数据库（chatgpt_user表）
- 提供用户信息查询API
- 基于token的用户身份验证
- RESTful API设计
- Docker容器化部署

## API接口

### 获取用户信息

```
GET /api/user/info?token=your_token_here
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 2,
    "userToken": "",
    "expireTime": "2025-12-31T23:59:59Z",
    "isPlus": true,
    "remark": "Test User",
    "allowModel": "gpt-4",
    "count": 100,
    "createTime": "2025-07-08T10:42:06Z",
    "updateTime": "2025-07-08T10:42:06Z"
  }
}
```

### 健康检查

```
GET /health
```

**响应示例：**
```json
{
  "status": "ok",
  "message": "Service is running"
}
```

## 使用Docker Compose运行

1. 构建并启动：
```bash
docker compose build
docker compose up -d
```

2. 查看日志：
```bash
docker compose logs api
```

3. 停止服务：
```bash
docker compose down
```

## 环境变量

- `DB_HOST`: 数据库主机地址（默认: chatgpt-share-server-deploy-mysql-1）
- `DB_PORT`: 数据库端口（默认: 3306）
- `DB_USER`: 数据库用户名（默认: cool）
- `DB_PASSWORD`: 数据库密码（默认: 123123）
- `DB_NAME`: 数据库名称（默认: cool）
- `PORT`: 服务器端口（默认: 8080）

## 数据库表结构

使用现有的`chatgpt_user`表：

```sql
-- 表结构
CREATE TABLE chatgpt_user (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    createTime datetime(3) NOT NULL,
    updateTime datetime(3) NOT NULL,
    deleted_at datetime(3) DEFAULT NULL,
    userToken longtext NOT NULL,
    expireTime datetime(3) NOT NULL,
    isPlus tinyint(1) DEFAULT 0,
    remark longtext,
    allowModel varchar(191) DEFAULT 'all',
    mygizmosIds json DEFAULT NULL,
    count bigint DEFAULT 0,
    PRIMARY KEY (id),
    KEY idx_chatgpt_user_deleted_at (deleted_at)
);
```

## 测试API

```bash
# 健康检查
curl "http://localhost:8080/health"

# 查询用户信息（需要有效的token）
curl "http://localhost:8080/api/user/info?token=test_token_123"

# 无效token测试
curl "http://localhost:8080/api/user/info?token=invalid_token"
```

## 项目结构

```
.
├── Dockerfile           # Docker镜像构建文件
├── docker-compose.yml   # Docker Compose配置
├── go.mod              # Go模块文件
├── go.sum              # Go依赖锁定文件
├── main.go             # 主要的Go应用程序
├── init.sql            # 数据库初始化脚本
└── README.md           # 项目说明文档
```

## 注意事项

- 本项目连接到现有的ChatGPT Share Server数据库
- 使用Docker网络连接到MySQL容器
- API响应中会自动隐藏用户的token信息
- 查询时会自动过滤已删除的用户记录（deleted_at IS NULL）