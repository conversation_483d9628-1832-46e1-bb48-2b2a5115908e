create table if not exists cdk_code (
    id int primary key auto_increment,
    
    code varchar(255) not null unique,
    code_type enum('create_user', 'extend_time') not null comment '兑换码类型：create_user=创建用户，extend_time=增加时长',
    status enum('unused', 'used', 'expired') default 'unused' comment '状态：unused=未使用，used=已使用，expired=已过期',
    days int not null default 0 comment '增加的天数',
    is_plus boolean default false comment '是否开启Plus权限',
    allow_model varchar(255) default 'all' comment '允许的模型',
    max_uses int default 1 comment '最大使用次数，默认为1',
    used_count int default 0 comment '已使用次数',
    expire_at timestamp null comment '兑换码过期时间',
    used_by_ids json null comment '使用者ID列表',
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp on update current_timestamp,
    
    index idx_code (code),
    index idx_status (status),
    index idx_code_type (code_type)
);