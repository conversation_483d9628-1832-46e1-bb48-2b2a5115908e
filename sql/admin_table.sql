-- 管理员表
create table if not exists admin_users (
    id int primary key auto_increment,
    username varchar(50) not null unique comment '管理员用户名',
    password_hash varchar(64) not null comment 'SHA256密码哈希',
    real_name varchar(100) null comment '真实姓名',
    email varchar(100) null comment '邮箱',
    status enum('active', 'disabled') default 'active' comment '状态：active=启用，disabled=禁用',
    last_login_at timestamp null comment '最后登录时间',
    login_count int default 0 comment '登录次数',
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp on update current_timestamp,
    
    index idx_username (username),
    index idx_status (status)
);

-- 插入默认管理员账户 (用户名: admin, 密码: admin123)
-- SHA256('admin123') = 240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9
INSERT IGNORE INTO admin_users (username, password_hash, real_name) 
VALUES ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '系统管理员'); 