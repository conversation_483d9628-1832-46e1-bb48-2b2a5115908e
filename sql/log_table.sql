-- 操作日志记录表
create table if not exists operation_logs (
    id int primary key auto_increment,
    operation_type enum('cdk_use', 'user_create', 'user_extend', 'cdk_validate', 'error') not null comment '操作类型',
    cdk_code varchar(255) null comment '兑换码',
    cdk_id int null comment '兑换码ID',
    user_id int null comment '用户ID',
    user_token varchar(255) null comment '用户Token',
    operation_result enum('success', 'failure') not null comment '操作结果',
    operation_details json null comment '操作详情',
    error_message text null comment '错误信息',
    ip_address varchar(45) null comment 'IP地址',
    user_agent text null comment '用户代理',
    request_data json null comment '请求数据',
    response_data json null comment '响应数据',
    execution_time_ms int null comment '执行时间(毫秒)',
    created_at timestamp default current_timestamp,
    
    index idx_operation_type (operation_type),
    index idx_cdk_code (cdk_code),
    index idx_user_id (user_id),
    index idx_created_at (created_at),
    index idx_operation_result (operation_result)
);

-- 兑换码用户关系记录表
create table if not exists cdk_user_relations (
    id int primary key auto_increment,
    cdk_id int not null comment '兑换码ID',
    cdk_code varchar(255) not null comment '兑换码',
    user_id int not null comment '用户ID',
    user_token varchar(255) not null comment '用户Token',
    relation_type enum('create', 'extend') not null comment '关系类型：create=创建用户，extend=增加时长',
    operation_details json null comment '操作详情',
    created_at timestamp default current_timestamp,
    
    index idx_cdk_id (cdk_id),
    index idx_cdk_code (cdk_code),
    index idx_user_id (user_id),
    index idx_relation_type (relation_type),
    unique key uk_cdk_user (cdk_id, user_id, relation_type)
); 